# Balance Endpoint Usage Examples

## Overview
The refactored `/v1/company/balance` endpoint returns paginated balances for companies owned by the authenticated user. This endpoint requires partner permissions (company owner access).

## Basic Usage

### Get First Page (Default)
```bash
curl -X GET "https://api.example.com/v1/company/balance" \
  -H "Authorization: Bearer YOUR_PARTNER_TOKEN" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "pageNumber": 1,
  "limit": 10,
  "totalItems": 25,
  "totalPages": 3,
  "data": [
    {
      "external_id": "01JR0X8RNYFJECMV1NBNVK9921",
      "name": "Mercadinho Caicó",
      "pix_key": "74312689000126",
      "is_active": true,
      "balance": 15000,
      "available_payout_count": 3,
      "latest_available_date": "2024-01-15T10:30:00Z"
    },
    {
      "external_id": "01JR0X8RNYFJECMV1NBNVK9922",
      "name": "Supermercado Central",
      "pix_key": "<EMAIL>",
      "is_active": false,
      "balance": 0,
      "available_payout_count": 0,
      "latest_available_date": null
    }
  ]
}
```

### Pagination Examples

#### Get Second Page with Custom Limit
```bash
curl -X GET "https://api.example.com/v1/company/balance?page=2&limit=5" \
  -H "Authorization: Bearer YOUR_PARTNER_TOKEN" \
  -H "Content-Type: application/json"
```

#### Get All User Companies (Large Limit)
```bash
curl -X GET "https://api.example.com/v1/company/balance?limit=100" \
  -H "Authorization: Bearer YOUR_PARTNER_TOKEN" \
  -H "Content-Type: application/json"
```

## Response Fields Explanation

| Field | Type | Description |
|-------|------|-------------|
| `external_id` | string | Company's external identifier (ULID) |
| `name` | string | Company name |
| `pix_key` | string | Company's PIX key for payments |
| `is_active` | boolean | Whether the company is active |
| `balance` | integer | Available balance in cents (centavos) |
| `available_payout_count` | integer | Number of available payouts |
| `latest_available_date` | string/null | ISO 8601 date of latest available payout |

## Balance Interpretation

- **Balance**: Amount in Brazilian centavos (divide by 100 for reais)
  - Example: `15000` = R$ 150.00
- **Available Payout Count**: Number of individual invoice payouts ready for withdrawal
- **Latest Available Date**: When the most recent payout became available (after 3-day holding period)

## Error Responses

### Invalid Pagination Parameters
```bash
curl -X GET "https://api.example.com/v1/company/balance?page=invalid" \
  -H "Authorization: Bearer YOUR_PARTNER_TOKEN"
```

**Response (400 Bad Request):**
```json
{
  "code": "001",
  "message": "invalid page"
}
```

### Limit Too High
```bash
curl -X GET "https://api.example.com/v1/company/balance?limit=200" \
  -H "Authorization: Bearer YOUR_PARTNER_TOKEN"
```

**Response (400 Bad Request):**
```json
{
  "code": "001",
  "message": "invalid limit parameter (must be between 1 and 100)"
}
```

### Unauthorized Access
```bash
curl -X GET "https://api.example.com/v1/company/balance" \
  -H "Authorization: Bearer INVALID_TOKEN"
```

**Response (403 Forbidden):**
```json
{
  "code": "001",
  "message": "insufficient permissions"
}
```

## JavaScript Example

```javascript
async function getUserCompaniesBalances(page = 1, limit = 10) {
  try {
    const response = await fetch(`/v1/company/balance?page=${page}&limit=${limit}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${partnerToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Process the balance data for user's companies
    data.data.forEach(company => {
      console.log(`${company.name}: R$ ${(company.balance / 100).toFixed(2)}`);
    });

    return data;
  } catch (error) {
    console.error('Error fetching user company balances:', error);
    throw error;
  }
}

// Usage
getUserCompaniesBalances(1, 20)
  .then(result => {
    console.log(`Found ${result.totalItems} companies owned by user across ${result.totalPages} pages`);
  })
  .catch(error => {
    console.error('Failed to fetch balances:', error);
  });
```

## Migration from Old Endpoint

### Old Endpoint (Deprecated)
- **Route**: `GET /v1/company/balance` (Partner access)
- **Response**: Single company balance for authenticated user

### New Endpoint
- **Route**: `GET /v1/company/balance` (Partner access)
- **Response**: Paginated list of companies owned by authenticated user with balances

### Migration Steps
1. Handle paginated response format instead of single company response
2. Update frontend to display multiple companies (if user owns multiple)
3. Implement pagination controls if needed
4. Authentication remains the same (partner tokens)
