
import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { companyService } from "@/services/api";

import { formatCurrency, formatDateTime } from "@/utils/formatters";
import {
  DollarSign,
  CreditCard,
  TrendingUp,
  RefreshCw,
  Loader2,
  AlertCircle,
  Calendar,
  Hash,
  Target
} from "lucide-react";
import WithdrawalModal from "./WithdrawalModal";
import { useAuth } from "@/contexts/AuthContext";

interface CompanyBillingTabProps {
  companyId?: string; // Optional for future use when we need company-specific data
}

const CompanyBillingTab: React.FC<CompanyBillingTabProps> = ({ companyId }) => {
  const [withdrawalModalOpen, setWithdrawalModalOpen] = useState(false);
  const { userRole } = useAuth();
  const isPartner = userRole === 'partner';

  // Fetch balance data
  const {
    data: balanceData,
    isLoading: isLoadingBalance,
    error: balanceError,
    refetch: refetchBalance
  } = useQuery({
    queryKey: ["company-balance", companyId],
    queryFn: async () => {
      const response = await companyService.getBalance();
      return response.data;
    },
    staleTime: 30000, // 30 seconds
  });

  // Fetch withdrawal history
  const {
    data: withdrawalHistory,
    isLoading: isLoadingHistory,
    error: historyError,
    refetch: refetchHistory
  } = useQuery({
    queryKey: ["withdrawal-history", companyId],
    queryFn: async () => {
      return await companyService.getWithdrawalHistory(1, 10);
    },
    staleTime: 60000, // 1 minute
  });

  // Calculate summary statistics
  const summaryStats = React.useMemo(() => {
    if (!withdrawalHistory?.data) {
      return {
        totalWithdrawn: 0,
        totalWithdrawals: 0,
        averageWithdrawal: 0,
        completedWithdrawals: 0
      };
    }

    const allWithdrawals = withdrawalHistory.data.withdrawals;
    const completedWithdrawals = allWithdrawals.filter(w => w.status === 'CONFIRMED');
    const completedTotalAmount = completedWithdrawals.reduce((sum, w) => sum + w.amount, 0);

    // Calculate totals based on completed withdrawals only
    const totalWithdrawn = withdrawalHistory.data.total_amount;
    const totalWithdrawals = allWithdrawals.length;
    const completedCount = completedWithdrawals.length;
    const averageWithdrawal = completedCount > 0 ? completedTotalAmount / completedCount : 0;

    return {
      totalWithdrawn,
      totalWithdrawals,
      averageWithdrawal,
      completedWithdrawals: completedCount
    };
  }, [withdrawalHistory]);

  // Handle refresh
  const handleRefresh = () => {
    refetchBalance();
    refetchHistory();
  };

  // Get status badge variant based on withdrawal status
  const getStatusBadgeVariant = (status: 'CREATED' | 'CONFIRMED' | 'FAILED'): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case 'CONFIRMED':
        return 'default'; // green for completed
      case 'CREATED':
        return 'secondary'; // gray for pending
      case 'FAILED':
        return 'destructive'; // red for failed
      default:
        return 'outline';
    }
  };

  // Get status label in Portuguese
  const getStatusLabel = (status: 'CREATED' | 'CONFIRMED' | 'FAILED') => {
    switch (status) {
      case 'CONFIRMED':
        return 'Concluído';
      case 'CREATED':
        return 'Pendente';
      case 'FAILED':
        return 'Falhou';
      default:
        return 'Desconhecido';
    }
  };

  const balance = balanceData?.balance || 0;
  const partnerName = balanceData?.name || '';
  const pixKey = balanceData?.pix_key || '';
  const canWithdraw = balance >= 50000;

  return (
    <div className="space-y-6">
      {/* Header with Refresh Button */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">Faturamento</h3>
          <p className="text-sm text-muted-foreground">
            Gerencie seu saldo e histórico de saques
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={isLoadingBalance || isLoadingHistory}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${(isLoadingBalance || isLoadingHistory) ? 'animate-spin' : ''}`} />
          Atualizar
        </Button>
      </div>

      {/* Section A: Current Balance Display */}
      <Card>
        <CardHeader>
        </CardHeader>
        <CardContent>
          {isLoadingBalance ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Carregando saldo...</span>
            </div>
          ) : balanceError ? (
            <div className="flex items-center justify-center py-8 text-red-500">
              <AlertCircle className="h-6 w-6 mr-2" />
              <span>Erro ao carregar saldo</span>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-600">Parceiro</p>
                  <p className="text-lg font-semibold">{partnerName}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-600">Chave PIX</p>
                  <p className="text-sm font-mono break-all">{pixKey}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-600">Saldo Disponível</p>
                  <p className="text-2xl font-bold text-green-600">
                    {formatCurrency(balance / 100)}
                  </p>
                </div>
              </div>

              {/* Withdrawal Button - Only for partners */}
              {isPartner && (
                <div className="pt-4 border-t">
                  <Button
                    onClick={() => setWithdrawalModalOpen(true)}
                    disabled={!canWithdraw}
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    <DollarSign className="mr-2 h-4 w-4" />
                    Sacar Saldo
                  </Button>
                  {!canWithdraw && (
                    <p className="text-sm text-muted-foreground mt-2">
                      Saldo mínimo para saque: R$ 500,00
                    </p>
                  )}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
      {/* Section C: Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Total Sacado
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">
              {formatCurrency(summaryStats.totalWithdrawn / 100)}
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              {summaryStats.completedWithdrawals} saques concluídos
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 flex items-center gap-2">
              <Hash className="h-4 w-4" />
              Total de Saques
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{summaryStats.totalWithdrawals}</p>
            <p className="text-xs text-muted-foreground mt-1">
              Incluindo pendentes e falhados
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 flex items-center gap-2">
              <Target className="h-4 w-4" />
              Saque Médio
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">
              {formatCurrency(summaryStats.averageWithdrawal / 100)}
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              Média dos saques concluídos
            </p>
          </CardContent>
        </Card>
      </div>
      {/* Section B: Withdrawal History Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Histórico de Saques
          </CardTitle>
          <CardDescription>
            Últimos saques realizados
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingHistory ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Carregando histórico...</span>
            </div>
          ) : historyError ? (
            <div className="flex items-center justify-center py-8 text-red-500">
              <AlertCircle className="h-6 w-6 mr-2" />
              <span>Erro ao carregar histórico</span>
            </div>
          ) : !withdrawalHistory?.data?.withdrawals.length ? (
            <div className="flex items-center justify-center py-8 text-muted-foreground">
              <Calendar className="h-6 w-6 mr-2" />
              <span>Nenhum saque realizado ainda</span>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2 px-4 font-medium">Data/Hora</th>
                    <th className="text-left py-2 px-4 font-medium">Valor</th>
                    <th className="text-left py-2 px-4 font-medium">Destino PIX</th>
                    <th className="text-left py-2 px-4 font-medium">Status</th>
                    <th className="text-left py-2 px-4 font-medium">ID</th>
                  </tr>
                </thead>
                <tbody>
                  {withdrawalHistory.data.withdrawals.map((withdrawal) => (
                    <tr key={withdrawal.correlation_id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="text-sm">
                          {formatDateTime(withdrawal.created_at)}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="font-semibold">
                          {formatCurrency(withdrawal.amount / 100)}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm font-mono">
                          {withdrawal.destination_alias}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <Badge variant={getStatusBadgeVariant(withdrawal.status)}>
                          {getStatusLabel(withdrawal.status)}
                        </Badge>
                        {withdrawal.status === 'CONFIRMED' && withdrawal.finished_at && (
                          <div className="text-xs text-muted-foreground mt-1">
                            Finalizado: {formatDateTime(withdrawal.finished_at)}
                          </div>
                        )}
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-xs text-muted-foreground font-mono">
                          {withdrawal.correlation_id}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Withdrawal Modal - Only for partners */}
      {isPartner && (
        <WithdrawalModal
          open={withdrawalModalOpen}
          onOpenChange={setWithdrawalModalOpen}
        />
      )}
    </div>
  );
};

export default CompanyBillingTab;
