import axios, { AxiosError, AxiosInstance } from "axios";
import { toast } from "sonner";
import {
  LoginRequest,
  LoginSuccessResponse,
  CreateCouponRequest,
  ListCouponsSuccessResponse,
  GetBalanceSuccessResponse,
  WithdrawRequest,
  WithdrawSuccessResponse,
  GetWithdrawalHistorySuccessResponse
} from "@/types/api";
import { saveTokenWithTimestamp } from "@/utils/tokenManager";

// Criação da instância do axios
export const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_NODE_ENV === "development" ? "http://localhost:8080" : "https://api.izymercado.com.br",
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  }
});

// Variável para controlar se já está tentando renovar o token
let isRefreshing = false;
// Variável para controlar se o usuário foi deslogado
let isLoggedOut = false;
// Fila de requisições que falharam por token expirado
let failedQueue: {
  resolve: (token: string) => void;
  reject: (error: unknown) => void;
}[] = [];

// Função para processar a fila de requisições que falharam
const processQueue = (error: Error | null, token: string | null = null) => {
  failedQueue.forEach((request) => {
    if (error) {
      request.reject(error);
    } else {
      request.resolve(token as string);
    }
  });
  
  failedQueue = [];
};

// Função para resetar o estado de logout (chamada no login bem-sucedido)
export const resetLogoutState = () => {
  isLoggedOut = false;
  isRefreshing = false;
  failedQueue = [];
};

// Configurando interceptador de requisição
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Configurando interceptador de resposta
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest: any = error.config;
    const errorData = error.response?.data as { message?: string };

    // Se o usuário já foi deslogado, não tenta mais nada
    if (isLoggedOut) {
      return Promise.reject(error);
    }

    // Verifica se é um erro 401 com "missing token" ou token expirado
    const is401WithMissingToken = error.response?.status === 401 &&
      (errorData?.message === "missing token" ||
       errorData?.message?.includes("token") ||
       errorData?.message?.includes("expired") ||
       errorData?.message?.includes("invalid"));

    if (is401WithMissingToken && !originalRequest._retry) {
      // Verifica se temos refresh token antes de tentar
      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) {
        // Só desconecta se realmente não tiver refresh token
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        toast.error("Sessão expirada. Por favor, faça login novamente.");
        return Promise.reject(new Error("AUTH_REQUIRED"));
      }

      if (isRefreshing) {
        // Se já estiver renovando o token, adiciona na fila
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            originalRequest.headers["Authorization"] = `Bearer ${token}`;
            return api(originalRequest);
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        console.log("🔄 Renovando token automaticamente...");

        // Chama diretamente o endpoint de refresh sem usar authService
        const refreshResponse = await axios.post(
          `${api.defaults.baseURL}/v1/auth/refresh`,
          { refresh_token: refreshToken },
          {
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'X-Application-Type': 'partner-web',
            },
            timeout: 10000
          }
        );

        const { access_token } = refreshResponse.data.data;

        if (!access_token) {
          throw new Error('No access token received from refresh');
        }

        // Salva o novo token com timestamp (mantém o mesmo refresh token)
        saveTokenWithTimestamp(access_token, 'access');

        // Atualiza o cabeçalho da requisição original
        api.defaults.headers.common["Authorization"] = `Bearer ${access_token}`;
        originalRequest.headers["Authorization"] = `Bearer ${access_token}`;

        // Processa a fila de requisições que falharam
        processQueue(null, access_token);

        isRefreshing = false;

        console.log("✅ Token renovado com sucesso!");

        // Refaz a requisição original com o novo token
        return api(originalRequest);
      } catch (refreshError) {
        console.error("❌ Erro ao renovar token:", refreshError);
        processQueue(refreshError as Error, null);
        isRefreshing = false;
        isLoggedOut = true; // Mark as logged out to prevent further attempts

        // Só limpa tokens e desconecta se o refresh realmente falhou
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        toast.error("Sessão expirada. Por favor, faça login novamente.", {
          dismissible: true,
        });
        return Promise.reject(new Error("AUTH_REQUIRED"));
      }
    }

    // Se o erro for 500, mostra mensagem de erro
    if (error.response?.status === 500) {
      toast.error("Erro no servidor. Por favor, tente novamente mais tarde.", {
        dismissible: true,
      });
    }

    // Se o erro for 400, mostra mensagem de erro
    if (error.response?.status === 400) {
      const data = error.response.data as { message: string };
      toast.error(data.message || "Erro na requisição. Verifique os dados informados.", {
        dismissible: true,
      });
    }

    return Promise.reject(error);
  }
);

// Serviço de autenticação
export const authService = {
  login: async (data: LoginRequest) => {
    const response = await api.post<LoginSuccessResponse>("/v1/auth/login", data, {
      headers: {
        "X-Application-Type": "partner-web",
      },
    });
    return response;
  },

  sendLoginCode: async (email: string) => {
    const response = await api.post("/v1/auth/send-login-code", { email });
    return response;
  },

  logout: async () => {
    const response = await api.post("/v1/auth/logout");
    return response;
  },
};

// Serviços de empresas
export const companyService = {
  getCompanies: async () => {
    return api.get("/v1/company/all");
  },

  getMyCompanies: async () => {
    return api.get("/v1/company/my-companies");
  },

  getCompany: async (externalID: string) => {
    return api.get(`/v1/company/${externalID}`);
  },

  getCompanyDetails: async (externalID: string) => {
    return api.get(`/v1/company/${externalID}/details`);
  },
  
  createCompany: async (formData: FormData) => {
    return api.post("/v1/company", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      }
    });
  },
  
  updateCompanyImage: async (externalID: string, formData: FormData) => {
    return api.patch(`/v1/company/${externalID}`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      }
    });
  },

  updateCompany: async (externalID: string, data: any) => {
    return api.put(`/v1/company/${externalID}`, data, {
      headers: {
        "Content-Type": "application/json",
      }
    });
  },

  addProductsToCompany: async (externalID: string, productIds: string[]) => {
    return api.put(`/v1/company/${externalID}/products`, {
      productIds,
    });
  },
  
  addProductToCompany: async (externalID: string, productData: {
    product_external_id: string;
    price: number;
    discount: number;
    stock: number;
  }) => {
    return api.put(`/v1/company/${externalID}/products`, {
      product_external_id: productData.product_external_id,
      price: productData.price,
      discount: productData.discount,
      stock: productData.stock,
    });
  },

  removeProductsFromCompany: async (externalID: string, productData: {
    product_external_id: string;
    price: number;
    discount: number;
    stock: number;
  }) => {
    return api.delete(`/v1/company/${externalID}/products`, {
      data: {
        product_external_id: productData.product_external_id,
        price: productData.price,
        discount: productData.discount,
        stock: productData.stock,
      }
    });
  },

  updateCompanyStatus: async (externalID: string, activate: boolean) => {
    return api.post(`/v1/company/${externalID}/status`, {
      activate
    });
  },

  // Search companies by name or CNPJ
  searchCompanies: async (query: string) => {
    return api.get(`/v1/company/search/${encodeURIComponent(query)}`);
  },

  // ========================================
  // BALANCE AND WITHDRAWAL METHODS
  // ========================================
  //
  // IMPORTANT: The following endpoints are not yet implemented in the backend:
  // - GET /v1/company/balance
  // - POST /v1/company/withdraw
  // - GET /v1/company/withdrawals
  //
  // These methods currently use mock data for development purposes.
  // When the backend implements these endpoints, remove the try-catch blocks
  // and use the direct API calls.
  //
  // Expected backend implementation:
  // 1. GET /v1/company/balance should return: { data: { balance: number, name: string, pix_key: string } }
  // 2. POST /v1/company/withdraw should accept: { amount: number } and return: { data: { message: string } }
  // 3. GET /v1/company/withdrawals should return paginated withdrawal history
  // ========================================

  // Balance method
  getBalance: async (): Promise<GetBalanceSuccessResponse> => {
    try {
      // Try to call the actual API endpoint first
      const response = await api.get<GetBalanceSuccessResponse>("/v1/company/balance");
      return response.data;
    } catch (error: any) {
      // If endpoint doesn't exist (404) or other API errors, return mock data for development
      if (error.response?.status === 404 || error.response?.status === 500) {
        console.warn('Balance endpoint not implemented yet, using mock data');

        // Return mock data for development
        const mockData: GetBalanceSuccessResponse = {
          data: {
            balance: 0, // R$ 2.250,00 (sum of completed withdrawals from mock history)
            name: "",
            pix_key: ""
          }
        };

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 300));

        return mockData;
      }

      // Re-throw other errors (network issues, auth errors, etc.)
      throw error;
    }
  },

  // Note: Backend endpoint /v1/company/withdraw is not implemented yet
  // This method will simulate the withdrawal until the backend implements the actual endpoint
  withdraw: async (data: WithdrawRequest): Promise<WithdrawSuccessResponse> => {
    try {
      // Try to call the actual API endpoint first
      const response = await api.post<WithdrawSuccessResponse>("/v1/company/withdraw", data);
      return response.data;
    } catch (error: any) {
      // If endpoint doesn't exist (404) or other API errors, simulate successful withdrawal for development
      if (error.response?.status === 404 || error.response?.status === 500) {
        console.warn('Withdraw endpoint not implemented yet, simulating successful withdrawal');

        // Simulate successful withdrawal response
        const mockResponse: WithdrawSuccessResponse = {
          data: {
            message: "Saque simulado com sucesso! O valor será processado em até 24 horas."
          }
        };

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        return mockResponse;
      }

      // Re-throw other errors (network issues, auth errors, etc.)
      throw error;
    }
  },

  // Withdrawal history method
  // Note: Backend endpoint /v1/company/withdrawals is not implemented yet
  // This method will return mock data until the backend implements the actual endpoint
  // Status field is not implemented yet - all withdrawals are treated as completed
  getWithdrawalHistory: async (page: number = 1, limit: number = 10): Promise<GetWithdrawalHistorySuccessResponse> => {
    try {
      // Try to call the actual API endpoint first
      const response = await api.get<GetWithdrawalHistorySuccessResponse>(`/v1/company/withdrawals?page=${page}&limit=${limit}`);
      return response.data;
    } catch (error: any) {
      throw error;
    }
  },
};

// Serviços de produtos
export const productService = {
  getProducts: (page?: number, limit?: number) =>
    api.get(`/v1/product?page=${page}&limit=${limit}`),
  createProduct: (formData: FormData) =>
    api.post("/v1/product", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }),
  updateProduct: (externalId: string, data: any) =>
    api.put(`/v1/product/${externalId}`, data, {
      headers: {
        "Content-Type": "application/json",
      },
    }),
  updateProductImage: (externalId: string, formData: FormData) =>
    api.patch(`/v1/product/${externalId}`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }),
  getProductByEan: (ean: string) =>
    api.get(`/v1/product/${ean}`),
  addProductToCompany: (companyId: string, formData: FormData) =>
    api.post(`/v1/company/${companyId}/product`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }),
};

// Serviço de cupons
export const couponService = {
  getCoupons: async (page: number = 1, limit: number = 10) => {
    return api.get<ListCouponsSuccessResponse>(`/v1/coupon?page=${page}&limit=${limit}`);
  },

  getCoupon: async (externalId: string) => {
    return api.get(`/v1/coupon/${externalId}`);
  },

  createCoupon: async (data: CreateCouponRequest) => {
    return api.post("/v1/coupon", data);
  },

  deleteCoupon: async (externalId: string) => {
    return api.delete(`/v1/coupon/${externalId}`);
  },

  updateStatus: async (externalId: string, status: boolean) => {
    return api.patch(`/v1/coupon/${externalId}/status?status=${status}`);
  }
};

// Serviço de categorias
export const categoryService = {
  getCategories: async (page: number = 1, limit: number = 10) => {
    return api.get(`/v1/category?page=${page}&limit=${limit}`);
  },

  createCategory: async (formData: FormData) => {
    return api.post("/v1/category", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  updateCategory: async (externalId: string, newName: string) => {
    return api.put(`/v1/category/${externalId}`, { name: newName }, {
      headers: {
        "Content-Type": "application/json",
      },
    });
  },

  updateCategoryImage: async (externalId: string, formData: FormData) => {
    return api.patch(`/v1/category/${externalId}`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  deleteCategory: async (externalId: string) => {
    return api.delete(`/v1/category/${externalId}`);
  }
};

// Serviço de pedidos/invoices
export const orderService = {
  // Get orders for current user's companies (partner/regular users)
  getOrders: async (page: number = 1, limit: number = 10) => {
    const url = `/v1/company/invoice?page=${page}&limit=${limit}`;
    console.log("🔄 orderService.getOrders API call:", { page, limit, url });
    return api.get(url);
  },

  // Get orders for admin users - fetches from all companies in the system
  getAdminOrders: async (page: number = 1, limit: number = 100) => {
    // For admin users, we need to get all companies first, then fetch orders
    // Since there's no direct "all orders" endpoint, we'll use the company invoice endpoint
    // but ensure admin users have access to all companies through their role
    return api.get(`/v1/company/invoice?page=${page}&limit=${limit}`);
  },

  // Get orders for a specific company (used in company details)
  getCompanyOrders: async (_companyExternalId: string, page: number = 1, limit: number = 100) => {
    // This will get orders filtered by company on the backend
    // For now, we'll use the same endpoint and filter client-side
    // TODO: Backend should provide a company-specific endpoint like /v1/company/{id}/invoice
    return api.get(`/v1/company/invoice?page=${page}&limit=${limit}`);
  },

  updateOrderStatus: async (orderId: string, data: { status: string; reason?: string }) => {
    return api.put(`/v1/company/invoice/${orderId}/status`, data);
  }
};

// Serviço de usuários
export const userService = {
  // Search users by query (for owner assignment and status management)
  searchUsers: async (query: string) => {
    return api.get(`/v1/user/search/${encodeURIComponent(query)}`);
  },

  // Create new user
  createUser: async (userData: {
    name: string;
    email: string;
    cpf: string;
    phone_numbers: string[];
  }) => {
    return api.post('/v1/user', userData);
  },

  // Check if email exists
  checkEmailExists: async (email: string) => {
    return api.post('/v1/user/exists/email', { email });
  },

  // Update user status (activate/deactivate)
  updateUserStatus: async (userExternalId: string, isActive: boolean) => {
    return api.patch(`/v1/user/${userExternalId}/status`, {
      is_active: isActive
    });
  },

  // Link user to company (for owner assignment)
  linkUserToCompany: async (companyExternalId: string, userExternalId: string) => {
    return api.put(`/v1/company/${companyExternalId}/owner`, {
      user_external_id: userExternalId
    });
  }
};
