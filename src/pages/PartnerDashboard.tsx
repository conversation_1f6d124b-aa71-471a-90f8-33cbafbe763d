import React from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { orderService, companyService } from "@/services/api";
import { ListOrdersSuccessResponse } from "@/types/api";
import { Building, Package, ShoppingBag, TrendingUp, Loader2 } from "lucide-react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { usePartnerData } from "@/hooks/usePartnerData";
import { useScreenSize } from "@/hooks/use-mobile";
import FCMTestPanel from "@/components/FCMTestPanel";
import WithdrawalModal from "@/components/WithdrawalModal";
import { DollarSign } from "lucide-react";

const PartnerDashboard = () => {
  const screenSize = useScreenSize();
  const [withdrawalModalOpen, setWithdrawalModalOpen] = React.useState(false);

  // Use the custom hook for partner data management
  const { companies: userCompaniesData, metrics, isLoading, companyIds, hasCache, debug } = usePartnerData();

  // Debug logging
  React.useEffect(() => {
    console.log('📊 [PartnerDashboard] State Update:', {
      isLoading,
      hasCache,
      companyIds,
      companiesCount: userCompaniesData?.length || 0,
      metrics,
      debug
    });
  }, [isLoading, hasCache, companyIds, userCompaniesData, metrics, debug]);

  // Busca pedidos para mostrar métricas
  const { data: ordersData, isLoading: isLoadingOrders } = useQuery({
    queryKey: ["partner-orders-metrics"],
    queryFn: async () => {
      const response = await orderService.getOrders(1, 100); // Buscar mais pedidos para métricas
      return response.data as ListOrdersSuccessResponse;
    },
  });

  // Busca histórico de saques para mostrar total sacado
  const { data: withdrawalHistory, isLoading: isLoadingWithdrawals } = useQuery({
    queryKey: ["partner-withdrawals-metrics"],
    queryFn: async () => {
      return await companyService.getWithdrawalHistory(1, 100); // Buscar histórico de saques
    },
  });

  // Filter orders to only include orders from partner's companies
  const partnerOrders = ordersData?.data?.filter(order =>
    companyIds.includes(order.company_external_id)
  ) || [];



  // Calcula métricas dos pedidos (apenas das empresas do parceiro)
  const orderMetrics = React.useMemo(() => {
    if (!partnerOrders.length) return { total: 0, pending: 0, completed: 0, revenue: 0 };

    const total = partnerOrders.length;
    const pending = partnerOrders.filter(order =>
      ['pending', 'processing', 'preparing', 'ready', 'delivering'].includes(order.status)
    ).length;
    const completed = partnerOrders.filter(order => order.status === 'completed').length;
    const revenue = partnerOrders
      .filter(order => order.status === 'completed')
      .reduce((sum, order) => sum + order.amount, 0) / 100; // Convert from centavos to reais

    return { total, pending, completed, revenue };
  }, [partnerOrders]);

  // Calcula métricas dos saques
  const withdrawalMetrics = React.useMemo(() => {
    if (!withdrawalHistory?.data?.withdrawals?.length) return { totalWithdrawn: 0, completedWithdrawals: 0 };

    const completedWithdrawals = withdrawalHistory.data.withdrawals.filter(w => w.status === 'CONFIRMED');
    const totalWithdrawn = completedWithdrawals.reduce((sum, w) => sum + w.amount, 0) / 100; // Convert from centavos to reais

    return { totalWithdrawn, completedWithdrawals: completedWithdrawals.length };
  }, [withdrawalHistory]);



  // Cards de resumo para o dashboard do parceiro
  const summaryCards = [
    {
      title: "Minhas Empresas",
      value: isLoading ? "..." : metrics.totalCompanies,
      icon: Building,
      description: "Empresas sob sua gestão",
      color: "bg-blue-500",
      href: "/partner/companies"
    },
    {
      title: "Total de Produtos",
      value: isLoading ? "..." : metrics.totalProducts,
      icon: Package,
      description: "Produtos em todas as empresas",
      color: "bg-green-500",
      href: "/partner/companies"
    },
    {
      title: "Pedidos Ativos",
      value: isLoadingOrders ? "..." : orderMetrics.pending,
      icon: ShoppingBag,
      description: "Pedidos em andamento",
      color: "bg-orange-500",
      href: "/partner/orders"
    },
    {
      title: "Total Sacado",
      value: isLoadingWithdrawals ? "..." : `R$ ${withdrawalMetrics.totalWithdrawn.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
      icon: TrendingUp,
      description: `${withdrawalMetrics.completedWithdrawals} saques concluídos`,
      color: "bg-purple-500",
    },
  ];

  return (
    <div className="space-y-6 sm:space-y-8">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
        <div>
          <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold tracking-tight">Dashboard do Parceiro</h2>
          <p className="text-sm sm:text-base text-muted-foreground mt-1">
            Gerencie suas empresas, produtos e pedidos
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={() => setWithdrawalModalOpen(true)}
            className="bg-green-600 hover:bg-green-700 text-white text-sm sm:text-base px-3 sm:px-4 py-2"
            size={screenSize === 'mobile' ? 'sm' : 'default'}
          >
            <DollarSign className="mr-1 sm:mr-2 h-4 w-4" />
            <span className="hidden xs:inline">Sacar Saldo</span>
            <span className="xs:hidden">Sacar</span>
          </Button>
        </div>
      </div>

      {/* Cards de resumo */}
      <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        {summaryCards.map((card, index) => (
          <Card key={index} className="overflow-hidden hover:shadow-lg transition-shadow">
            <CardHeader className={`${card.color} text-white p-4 sm:p-5`}>
              <div className="flex justify-between items-center">
                <CardTitle className="text-base sm:text-lg font-medium">{card.title}</CardTitle>
                <card.icon size={screenSize === 'mobile' ? 18 : 20} />
              </div>
            </CardHeader>
            <CardContent className="p-4 sm:p-5">
              <div className="text-xl sm:text-2xl font-bold mb-2">{card.value}</div>
              <CardDescription className="mb-3 text-sm sm:text-base">{card.description}</CardDescription>
              {card.href && (
                <Link to={card.href}>
                  <Button variant="outline" size="sm" className="w-full touch-manipulation min-h-[44px]">
                    <span className="text-sm sm:text-base">Ver detalhes</span>
                  </Button>
                </Link>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Lista das empresas do usuário */}
      <Card>
        <CardHeader className="p-4 sm:p-6">
          <CardTitle className="flex items-center text-lg sm:text-xl">
            <Building className="mr-2" size={screenSize === 'mobile' ? 18 : 20} />
            Suas Empresas
          </CardTitle>
          <CardDescription className="text-sm sm:text-base">
            Empresas que você tem permissão para gerenciar
          </CardDescription>
        </CardHeader>
        <CardContent className="p-4 sm:p-6">
          {isLoading ? (
            <div className="text-center py-8">
              <Loader2 className="mx-auto h-6 w-6 animate-spin mb-2" />
              <p className="text-sm sm:text-base">Carregando empresas...</p>
            </div>
          ) : (Array.isArray(userCompaniesData) && userCompaniesData.length > 0) ? (
            <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
              {userCompaniesData.map((company) => (
                <Card key={company.external_id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-4 sm:p-5">
                    <div className="flex items-start space-x-3">
                      {company.picture ? (
                        <img
                          src={company.picture}
                          alt={company.name}
                          className="w-10 h-10 sm:w-12 sm:h-12 rounded-lg object-cover flex-shrink-0"
                        />
                      ) : (
                        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0">
                          <Building size={screenSize === 'mobile' ? 16 : 20} className="text-gray-500" />
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-sm sm:text-base truncate">{company.name}</h3>
                        <p className="text-xs sm:text-sm text-muted-foreground">
                          {company.products?.length || 0} produtos
                        </p>
                        <div className="flex items-center mt-2">
                          {company.is_active ? (
                            <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                              Ativa
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                              Inativa
                            </span>
                          )}
                        </div>
                        <Link to={`/partner/companies/${company.external_id}`}>
                          <Button variant="outline" size="sm" className="mt-2 w-full">
                            Gerenciar
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Building size={48} className="mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500 mb-2">Nenhuma empresa encontrada</p>
              <p className="text-sm text-gray-400">
                Você não tem permissão para gerenciar nenhuma empresa no momento.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Resumo de pedidos recentes */}
      {partnerOrders.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ShoppingBag className="mr-2" size={20} />
              Pedidos Recentes
            </CardTitle>
            <CardDescription>
              Últimos pedidos das suas empresas
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {partnerOrders.slice(0, 5).map((order) => (
                <div key={order.order_id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-sm">Pedido #{order.order_id.slice(-8)}</p>
                    <p className="text-xs text-muted-foreground">{order.user_name}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-sm">
                      R$ {(order.amount / 100).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                    </p>
                    <p className="text-xs text-muted-foreground capitalize">{order.status}</p>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4">
              <Link to="/partner/orders">
                <Button variant="outline" className="w-full">
                  Ver todos os pedidos
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}

      {/* FCM Test Panel for Development - Partner Users Only */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-8">
          <FCMTestPanel />
        </div>
      )}

      {/* Withdrawal Modal */}
      <WithdrawalModal
        open={withdrawalModalOpen}
        onOpenChange={setWithdrawalModalOpen}
      />
    </div>
  );
};

export default PartnerDashboard;
