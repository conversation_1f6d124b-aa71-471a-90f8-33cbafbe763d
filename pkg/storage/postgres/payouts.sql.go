// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: payouts.sql

package postgres

import (
	"context"
	"database/sql"
	"time"

	"github.com/jackc/pgtype"
)

const allocatePayoutsForWithdrawal = `-- name: AllocatePayoutsForWithdrawal :exec
UPDATE invoice_payouts 
SET status = 'withdrawn', updated_at = NOW()
WHERE id = ANY($1::BIGINT[]) 
  AND status = 'available' 
  AND company_id = $2
`

type AllocatePayoutsForWithdrawalParams struct {
	Column1   []int64
	CompanyID int32
}

func (q *Queries) AllocatePayoutsForWithdrawal(ctx context.Context, arg AllocatePayoutsForWithdrawalParams) error {
	_, err := q.db.Exec(ctx, allocatePayoutsForWithdrawal, arg.Column1, arg.CompanyID)
	return err
}

const blockPayouts = `-- name: BlockPayouts :exec
UPDATE invoice_payouts 
SET status = 'blocked', updated_at = NOW()
WHERE id = ANY($1::BIGINT[]) 
  AND status IN ('pending', 'available')
`

func (q *Queries) BlockPayouts(ctx context.Context, dollar_1 []int64) error {
	_, err := q.db.Exec(ctx, blockPayouts, dollar_1)
	return err
}

const createWithdrawalWithPayouts = `-- name: CreateWithdrawalWithPayouts :one
WITH new_withdrawal AS (
  INSERT INTO company_withdrawals (
    company_id,
    amount,
    correlation_id,
    destination_pix_key,
    comment,
    end_to_end_id,
    woovi_response
  ) VALUES (
    $1, $2, $3, $4, $5, $6, $7
  ) RETURNING id, created_at
),
payout_links AS (
  INSERT INTO withdrawal_payouts (withdrawal_id, payout_id)
  SELECT 
    (SELECT id FROM new_withdrawal),
    unnest($8::BIGINT[])
  RETURNING withdrawal_id, payout_id
)
SELECT 
  nw.id,
  nw.created_at,
  array_agg(pl.payout_id) as linked_payout_ids
FROM new_withdrawal nw
LEFT JOIN payout_links pl ON pl.withdrawal_id = nw.id
GROUP BY nw.id, nw.created_at
`

type CreateWithdrawalWithPayoutsParams struct {
	CompanyID         int64
	Amount            int32
	CorrelationID     sql.NullString
	DestinationPixKey sql.NullString
	Comment           sql.NullString
	EndToEndID        sql.NullString
	WooviResponse     pgtype.JSONB
	Column8           []int64
}

type CreateWithdrawalWithPayoutsRow struct {
	ID              int64
	CreatedAt       time.Time
	LinkedPayoutIds interface{}
}

func (q *Queries) CreateWithdrawalWithPayouts(ctx context.Context, arg CreateWithdrawalWithPayoutsParams) (CreateWithdrawalWithPayoutsRow, error) {
	row := q.db.QueryRow(ctx, createWithdrawalWithPayouts,
		arg.CompanyID,
		arg.Amount,
		arg.CorrelationID,
		arg.DestinationPixKey,
		arg.Comment,
		arg.EndToEndID,
		arg.WooviResponse,
		arg.Column8,
	)
	var i CreateWithdrawalWithPayoutsRow
	err := row.Scan(&i.ID, &i.CreatedAt, &i.LinkedPayoutIds)
	return i, err
}

const getAllCompaniesBalances = `-- name: GetAllCompaniesBalances :many
SELECT
    c.id,
    c.external_id,
    c.name,
    c.pix_key,
    c.is_active,
    COALESCE(cab.available_balance, 0) as available_balance,
    COALESCE(cab.available_payout_count, 0) as available_payout_count,
    cab.latest_available_date,
    COUNT(*) OVER() AS total_count
FROM companies c
LEFT JOIN company_available_balances cab ON c.id = cab.company_id
ORDER BY c.name ASC
LIMIT $1 OFFSET $2
`

type GetAllCompaniesBalancesParams struct {
	Limit  int32
	Offset int32
}

type GetAllCompaniesBalancesRow struct {
	ID                   int32
	ExternalID           string
	Name                 string
	PixKey               string
	IsActive             bool
	AvailableBalance     interface{}
	AvailablePayoutCount int64
	LatestAvailableDate  interface{}
	TotalCount           int64
}

func (q *Queries) GetAllCompaniesBalances(ctx context.Context, arg GetAllCompaniesBalancesParams) ([]GetAllCompaniesBalancesRow, error) {
	rows, err := q.db.Query(ctx, getAllCompaniesBalances, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllCompaniesBalancesRow
	for rows.Next() {
		var i GetAllCompaniesBalancesRow
		if err := rows.Scan(
			&i.ID,
			&i.ExternalID,
			&i.Name,
			&i.PixKey,
			&i.IsActive,
			&i.AvailableBalance,
			&i.AvailablePayoutCount,
			&i.LatestAvailableDate,
			&i.TotalCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getCompanyAvailableBalance = `-- name: GetCompanyAvailableBalance :one
SELECT 
    COALESCE(SUM(amount), 0) as available_balance,
    COUNT(*) as available_payout_count
FROM invoice_payouts 
WHERE company_id = $1 
  AND status = 'available' 
  AND available_after <= NOW()
`

type GetCompanyAvailableBalanceRow struct {
	AvailableBalance     interface{}
	AvailablePayoutCount int64
}

func (q *Queries) GetCompanyAvailableBalance(ctx context.Context, companyID int32) (GetCompanyAvailableBalanceRow, error) {
	row := q.db.QueryRow(ctx, getCompanyAvailableBalance, companyID)
	var i GetCompanyAvailableBalanceRow
	err := row.Scan(&i.AvailableBalance, &i.AvailablePayoutCount)
	return i, err
}

const getCompanyAvailableBalanceFast = `-- name: GetCompanyAvailableBalanceFast :one
SELECT 
    COALESCE(available_balance, 0) as available_balance,
    COALESCE(available_payout_count, 0) as available_payout_count,
    latest_available_date
FROM company_available_balances 
WHERE company_id = $1
`

type GetCompanyAvailableBalanceFastRow struct {
	AvailableBalance     interface{}
	AvailablePayoutCount int64
	LatestAvailableDate  interface{}
}

func (q *Queries) GetCompanyAvailableBalanceFast(ctx context.Context, companyID int32) (GetCompanyAvailableBalanceFastRow, error) {
	row := q.db.QueryRow(ctx, getCompanyAvailableBalanceFast, companyID)
	var i GetCompanyAvailableBalanceFastRow
	err := row.Scan(&i.AvailableBalance, &i.AvailablePayoutCount, &i.LatestAvailableDate)
	return i, err
}

const getCompanyPayoutHistory = `-- name: GetCompanyPayoutHistory :many
WITH company_info AS (
  SELECT id, external_id, name
  FROM companies
  WHERE external_id = $1
),
period_filter AS (
  SELECT
    CASE
      WHEN $4::text = '1m' THEN NOW() - INTERVAL '1 month'
      WHEN $4::text = '3m' THEN NOW() - INTERVAL '3 months'
      WHEN $4::text = '6m' THEN NOW() - INTERVAL '6 months'
      WHEN $4::text = '1y' THEN NOW() - INTERVAL '1 year'
      ELSE NOW() - INTERVAL '30 days' -- Default to 30 days
    END AS start_date
),
filtered_payouts AS (
  SELECT
    ip.id,
    ip.invoice_id,
    ip.status,
    ip.amount,
    ip.finalized_at,
    ip.available_after,
    ip.created_at,
    i.order_id,
    ci.external_id as company_external_id,
    ci.name as company_name,
    CASE 
      WHEN wp.withdrawal_id IS NOT NULL THEN wp.withdrawal_id
      ELSE NULL
    END as withdrawal_id
  FROM invoice_payouts ip
  JOIN company_info ci ON ip.company_id = ci.id
  JOIN invoices i ON i.id = ip.invoice_id
  LEFT JOIN withdrawal_payouts wp ON wp.payout_id = ip.id
  CROSS JOIN period_filter pf
  WHERE ip.created_at >= pf.start_date
  ORDER BY ip.created_at DESC
),
total_info AS (
  SELECT
    COUNT(*) as total_count,
    COALESCE(SUM(amount), 0) as total_amount
  FROM filtered_payouts
)
SELECT
  fp.id,
  fp.invoice_id,
  fp.status,
  fp.amount,
  fp.finalized_at,
  fp.available_after,
  fp.created_at,
  fp.order_id,
  fp.company_external_id,
  fp.company_name,
  fp.withdrawal_id,
  ti.total_count,
  ti.total_amount
FROM filtered_payouts fp
CROSS JOIN total_info ti
LIMIT $2 OFFSET $3
`

type GetCompanyPayoutHistoryParams struct {
	ExternalID string
	Limit      int32
	Offset     int32
	Column4    string
}

type GetCompanyPayoutHistoryRow struct {
	ID                int64
	InvoiceID         int64
	Status            string
	Amount            int32
	FinalizedAt       sql.NullTime
	AvailableAfter    sql.NullTime
	CreatedAt         time.Time
	OrderID           string
	CompanyExternalID string
	CompanyName       string
	WithdrawalID      interface{}
	TotalCount        int64
	TotalAmount       interface{}
}

func (q *Queries) GetCompanyPayoutHistory(ctx context.Context, arg GetCompanyPayoutHistoryParams) ([]GetCompanyPayoutHistoryRow, error) {
	rows, err := q.db.Query(ctx, getCompanyPayoutHistory,
		arg.ExternalID,
		arg.Limit,
		arg.Offset,
		arg.Column4,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetCompanyPayoutHistoryRow
	for rows.Next() {
		var i GetCompanyPayoutHistoryRow
		if err := rows.Scan(
			&i.ID,
			&i.InvoiceID,
			&i.Status,
			&i.Amount,
			&i.FinalizedAt,
			&i.AvailableAfter,
			&i.CreatedAt,
			&i.OrderID,
			&i.CompanyExternalID,
			&i.CompanyName,
			&i.WithdrawalID,
			&i.TotalCount,
			&i.TotalAmount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getCompanyPayoutSummary = `-- name: GetCompanyPayoutSummary :one
SELECT 
    company_id,
    COUNT(*) as total_payouts,
    COALESCE(SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END), 0) as pending_amount,
    COALESCE(SUM(CASE WHEN status = 'available' THEN amount ELSE 0 END), 0) as available_amount,
    COALESCE(SUM(CASE WHEN status = 'withdrawn' THEN amount ELSE 0 END), 0) as withdrawn_amount,
    COALESCE(SUM(CASE WHEN status = 'blocked' THEN amount ELSE 0 END), 0) as blocked_amount,
    MIN(available_after) as earliest_available_date,
    MAX(available_after) as latest_available_date
FROM invoice_payouts 
WHERE company_id = $1
GROUP BY company_id
`

type GetCompanyPayoutSummaryRow struct {
	CompanyID             int32
	TotalPayouts          int64
	PendingAmount         interface{}
	AvailableAmount       interface{}
	WithdrawnAmount       interface{}
	BlockedAmount         interface{}
	EarliestAvailableDate interface{}
	LatestAvailableDate   interface{}
}

func (q *Queries) GetCompanyPayoutSummary(ctx context.Context, companyID int32) (GetCompanyPayoutSummaryRow, error) {
	row := q.db.QueryRow(ctx, getCompanyPayoutSummary, companyID)
	var i GetCompanyPayoutSummaryRow
	err := row.Scan(
		&i.CompanyID,
		&i.TotalPayouts,
		&i.PendingAmount,
		&i.AvailableAmount,
		&i.WithdrawnAmount,
		&i.BlockedAmount,
		&i.EarliestAvailableDate,
		&i.LatestAvailableDate,
	)
	return i, err
}

const getEligiblePayoutsForWithdrawal = `-- name: GetEligiblePayoutsForWithdrawal :many
SELECT 
    id,
    invoice_id,
    company_id,
    amount,
    finalized_at,
    available_after,
    created_at
FROM invoice_payouts 
WHERE company_id = $1 
  AND status = 'available' 
  AND available_after <= NOW()
ORDER BY available_after ASC
FOR UPDATE SKIP LOCKED
`

type GetEligiblePayoutsForWithdrawalRow struct {
	ID             int64
	InvoiceID      int64
	CompanyID      int32
	Amount         int32
	FinalizedAt    sql.NullTime
	AvailableAfter sql.NullTime
	CreatedAt      time.Time
}

func (q *Queries) GetEligiblePayoutsForWithdrawal(ctx context.Context, companyID int32) ([]GetEligiblePayoutsForWithdrawalRow, error) {
	rows, err := q.db.Query(ctx, getEligiblePayoutsForWithdrawal, companyID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetEligiblePayoutsForWithdrawalRow
	for rows.Next() {
		var i GetEligiblePayoutsForWithdrawalRow
		if err := rows.Scan(
			&i.ID,
			&i.InvoiceID,
			&i.CompanyID,
			&i.Amount,
			&i.FinalizedAt,
			&i.AvailableAfter,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getPayoutsByInvoiceIDs = `-- name: GetPayoutsByInvoiceIDs :many
SELECT 
    id,
    invoice_id,
    company_id,
    status,
    amount,
    finalized_at,
    available_after,
    created_at,
    updated_at
FROM invoice_payouts 
WHERE invoice_id = ANY($1::BIGINT[])
ORDER BY created_at DESC
`

func (q *Queries) GetPayoutsByInvoiceIDs(ctx context.Context, dollar_1 []int64) ([]InvoicePayout, error) {
	rows, err := q.db.Query(ctx, getPayoutsByInvoiceIDs, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []InvoicePayout
	for rows.Next() {
		var i InvoicePayout
		if err := rows.Scan(
			&i.ID,
			&i.InvoiceID,
			&i.CompanyID,
			&i.Status,
			&i.Amount,
			&i.FinalizedAt,
			&i.AvailableAfter,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getWithdrawalWithPayouts = `-- name: GetWithdrawalWithPayouts :one
SELECT 
    cw.id,
    cw.company_id,
    cw.amount,
    cw.status,
    cw.correlation_id,
    cw.destination_pix_key,
    cw.comment,
    cw.end_to_end_id,
    cw.woovi_response,
    cw.finished_at,
    cw.created_at,
    c.external_id as company_external_id,
    c.name as company_name,
    json_agg(
        DISTINCT jsonb_build_object(
            'payout_id', ip.id,
            'invoice_id', ip.invoice_id,
            'amount', ip.amount,
            'finalized_at', ip.finalized_at,
            'available_after', ip.available_after,
            'order_id', i.order_id
        )
    ) FILTER (WHERE ip.id IS NOT NULL) as payouts
FROM company_withdrawals cw
JOIN companies c ON c.id = cw.company_id
LEFT JOIN withdrawal_payouts wp ON wp.withdrawal_id = cw.id
LEFT JOIN invoice_payouts ip ON ip.id = wp.payout_id
LEFT JOIN invoices i ON i.id = ip.invoice_id
WHERE cw.id = $1
GROUP BY cw.id, c.external_id, c.name
`

type GetWithdrawalWithPayoutsRow struct {
	ID                int64
	CompanyID         int64
	Amount            int32
	Status            string
	CorrelationID     sql.NullString
	DestinationPixKey sql.NullString
	Comment           sql.NullString
	EndToEndID        sql.NullString
	WooviResponse     pgtype.JSONB
	FinishedAt        sql.NullTime
	CreatedAt         time.Time
	CompanyExternalID string
	CompanyName       string
	Payouts           pgtype.JSON
}

func (q *Queries) GetWithdrawalWithPayouts(ctx context.Context, id int64) (GetWithdrawalWithPayoutsRow, error) {
	row := q.db.QueryRow(ctx, getWithdrawalWithPayouts, id)
	var i GetWithdrawalWithPayoutsRow
	err := row.Scan(
		&i.ID,
		&i.CompanyID,
		&i.Amount,
		&i.Status,
		&i.CorrelationID,
		&i.DestinationPixKey,
		&i.Comment,
		&i.EndToEndID,
		&i.WooviResponse,
		&i.FinishedAt,
		&i.CreatedAt,
		&i.CompanyExternalID,
		&i.CompanyName,
		&i.Payouts,
	)
	return i, err
}

const refreshCompanyBalancesView = `-- name: RefreshCompanyBalancesView :exec
REFRESH MATERIALIZED VIEW company_available_balances
`

func (q *Queries) RefreshCompanyBalancesView(ctx context.Context) error {
	_, err := q.db.Exec(ctx, refreshCompanyBalancesView)
	return err
}

const unblockPayouts = `-- name: UnblockPayouts :exec
UPDATE invoice_payouts 
SET status = CASE 
    WHEN available_after <= NOW() THEN 'available'
    ELSE 'pending'
END,
updated_at = NOW()
WHERE id = ANY($1::BIGINT[]) 
  AND status = 'blocked'
`

func (q *Queries) UnblockPayouts(ctx context.Context, dollar_1 []int64) error {
	_, err := q.db.Exec(ctx, unblockPayouts, dollar_1)
	return err
}

const updatePayoutStatus = `-- name: UpdatePayoutStatus :exec
UPDATE invoice_payouts 
SET status = $2, updated_at = NOW()
WHERE id = $1
`

type UpdatePayoutStatusParams struct {
	ID     int64
	Status string
}

func (q *Queries) UpdatePayoutStatus(ctx context.Context, arg UpdatePayoutStatusParams) error {
	_, err := q.db.Exec(ctx, updatePayoutStatus, arg.ID, arg.Status)
	return err
}
